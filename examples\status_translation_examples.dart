import 'package:flutter/material.dart';
import '../lib/src/config/app_localizations.dart';

/// Exemples d'utilisation des nouvelles traductions de statuts
class StatusTranslationExamples {
  
  /// Exemple 1: Affichage des statuts d'appeals
  Widget buildAppealStatusExample(BuildContext context, String status) {
    final localizations = AppLocalizations.of(context);
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: _getStatusColor(status),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        localizations?.getAppealStatusLabel(status) ?? status.toUpperCase(),
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  /// Exemple 2: Affichage des statuts de requests
  Widget buildRequestStatusExample(BuildContext context, String status) {
    final localizations = AppLocalizations.of(context);
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: _getStatusColor(status),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        localizations?.getRequestStatusLabel(status) ?? status.toUpperCase(),
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  /// Exemple 3: Dialogue de confirmation pour appeal
  Future<bool?> showAppealConfirmationDialog(
    BuildContext context,
    String action, // 'approve' ou 'reject'
  ) {
    final localizations = AppLocalizations.of(context);
    
    String titleKey = action == 'approve' 
        ? 'approve_appeal_title' 
        : 'reject_appeal_title';
    String messageKey = action == 'approve' 
        ? 'approve_appeal_message' 
        : 'reject_appeal_message';
    
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            localizations?.translate(titleKey) ?? 
            (action == 'approve' ? 'Approve Appeal' : 'Reject Appeal'),
          ),
          content: Text(
            localizations?.translate(messageKey) ?? 
            'Are you sure you want to $action this appeal?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                localizations?.translate('cancel') ?? 'Cancel',
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                localizations?.translate('confirm') ?? 'Confirm',
              ),
            ),
          ],
        );
      },
    );
  }
  
  /// Exemple 4: Dialogue de confirmation pour request
  Future<bool?> showRequestConfirmationDialog(
    BuildContext context,
    String action, // 'approve' ou 'reject'
  ) {
    final localizations = AppLocalizations.of(context);
    
    String titleKey = action == 'approve' 
        ? 'approve_request_title' 
        : 'reject_request_title';
    String messageKey = action == 'approve' 
        ? 'approve_request_message' 
        : 'reject_request_message';
    
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            localizations?.translate(titleKey) ?? 
            (action == 'approve' ? 'Approve Request' : 'Reject Request'),
          ),
          content: Text(
            localizations?.translate(messageKey) ?? 
            'Are you sure you want to $action this request?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                localizations?.translate('cancel') ?? 'Cancel',
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                localizations?.translate('confirm') ?? 'Confirm',
              ),
            ),
          ],
        );
      },
    );
  }
  
  /// Exemple 5: Liste des statuts avec traductions
  Widget buildStatusList(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final statuses = ['approved', 'rejected', 'pending'];
    
    return Column(
      children: [
        Text(
          'Appeals Status:',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        ...statuses.map((status) => ListTile(
          title: Text(localizations?.getAppealStatusLabel(status) ?? status),
          leading: Icon(
            _getStatusIcon(status),
            color: _getStatusColor(status),
          ),
        )),
        const SizedBox(height: 20),
        Text(
          'Requests Status:',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        ...statuses.map((status) => ListTile(
          title: Text(localizations?.getRequestStatusLabel(status) ?? status),
          leading: Icon(
            _getStatusIcon(status),
            color: _getStatusColor(status),
          ),
        )),
      ],
    );
  }
  
  /// Méthodes utilitaires
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
  
  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Icons.check_circle;
      case 'rejected':
        return Icons.cancel;
      case 'pending':
        return Icons.access_time;
      default:
        return Icons.help;
    }
  }
}

/// Exemples de résultats attendus selon la langue

/*
FRANÇAIS:
- Appeals: "RECOURS APPROUVÉ", "RECOURS REJETÉ", "RECOURS EN ATTENTE"
- Requests: "DEMANDE APPROUVÉE", "DEMANDE REJETÉE", "DEMANDE EN ATTENTE"
- Dialogues: "Approuver le recours", "Rejeter la demande", etc.

ARABE:
- Appeals: "طعن موافق عليه", "طعن مرفوض", "طعن قيد الانتظار"
- Requests: "طلب موافق عليه", "طلب مرفوض", "طلب قيد الانتظار"
- Dialogues: "موافقة على الطعن", "رفض الطلب", etc.

ANGLAIS:
- Appeals: "APPEAL APPROVED", "APPEAL REJECTED", "APPEAL PENDING"
- Requests: "REQUEST APPROVED", "REQUEST REJECTED", "REQUEST PENDING"
- Dialogues: "Approve Appeal", "Reject Request", etc.
*/
