import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:universal_html/html.dart' as html;
import '../src/config/app_localizations.dart';
import '../src/config/theme_helper.dart';
import 'adminProfil.dart';
import 'locale_provider.dart';
import 'login.dart';

class AppColors {
  static const Color primaryOrange = Color(0xFFFF5722);
  static const Color pureWhite = Color(0xFFFFFFFF);
  static const Color orangeLight = Color(0xFFFF8A65);
  static const Color orangePale = Color(0xFFFFEBE7);
  static const Color darkGray = Color(0xFF212121);
  static const Color mediumGray = Color(0xFF757575);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color borderGray = Color(0xFFE0E0E0);
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFD32F2F);
  static const Color warning = Color(0xFFFFA726);
}

class DashboardApp extends StatelessWidget {
  const DashboardApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const Dashboard();
  }
}

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> with SingleTickerProviderStateMixin {
  final _storage = const FlutterSecureStorage();
  late TabController _tabController;
  
  List<dynamic> _requests = [];
  List<dynamic> _appeals = [];
  List<dynamic> _permits = [];
  bool _isLoading = true;
  String? _errorMessage;
  
  Map<String, int> _stats = {
    'total_requests': 0,
    'pending_requests': 0,
    'approved_requests': 0,
    'rejected_requests': 0,
    'total_appeals': 0,
    'pending_appeals': 0,
    'total_permits': 0,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _fetchAllData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchAllData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) throw Exception('No token found');

      final headers = {
        'Authorization': 'Bearer $token',
        'Accept': 'application/json',
      };

      final List<Future<http.Response>> futures = [
        http.get(Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/admin/requests'), headers: headers),
        http.get(Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/admin/appeals'), headers: headers),
        http.get(Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/admin/permits'), headers: headers),
      ];

      final responses = await Future.wait(futures);
      
      for (var response in responses) {
        if (response.statusCode == 401) {
          await _handleAuthError();
          return;
        }
      }

      if (responses.every((r) => r.statusCode == 200)) {
        final requestsData = jsonDecode(responses[0].body) as Map<String, dynamic>?;
        final appealsData = jsonDecode(responses[1].body) as Map<String, dynamic>?;
        final permitsData = jsonDecode(responses[2].body) as Map<String, dynamic>?;
        
        print('Requests response: $requestsData');
        print('Appeals response: $appealsData');
        print('Permits response: $permitsData');

        setState(() {
          _requests = _parseList(requestsData?['data']) ?? [];
          _appeals = _parseList(appealsData?['data']) ?? [];
          _permits = _parseList(permitsData?['data']) ?? [];
          _updateStats();
          _isLoading = false;
        });
      } else {
        throw Exception('Failed to load data. Status codes: ${responses.map((r) => r.statusCode).join(', ')}');
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  List<dynamic>? _parseList(dynamic data) {
    if (data is List) {
      return data;
    } else if (data is Map) {
      return _parseList(data['data']);
    }
    print('Warning: Unexpected data type for list: $data');
    return null;
  }

  void _updateStats() {
    setState(() {
      _stats['total_requests'] = _requests.length;
      _stats['pending_requests'] = _requests.where((r) => (r as Map<String, dynamic>?)?['status'] == 'pending').length;
      _stats['approved_requests'] = _requests.where((r) => (r as Map<String, dynamic>?)?['status'] == 'approved').length;
      _stats['rejected_requests'] = _requests.where((r) => (r as Map<String, dynamic>?)?['status'] == 'rejected').length;
      _stats['total_appeals'] = _appeals.length;
      _stats['pending_appeals'] = _appeals.where((a) => (a as Map<String, dynamic>?)?['status'] == 'pending').length;
      _stats['total_permits'] = _permits.length;
    });
  }

  Future<void> _handleAuthError() async {
    await _storage.delete(key: 'auth_token');
    if (mounted) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const LoginApp()),
        (route) => false,
      );
    }
  }

  Future<void> _updateRequestStatus(String requestId, String status) async {
    if (requestId.isEmpty) {
      _showErrorSnackBar('Invalid request ID');
      return;
    }
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) throw Exception('No token found');

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/admin/requests/$requestId/status'),
      );
      
      request.headers.addAll({
        'Authorization': 'Bearer $token',
        'Accept': 'application/json',
      });
      
      request.fields['status'] = status;

      // Handle file upload for approval
      if (status == 'approved') {
        File? pdfFile;
        if (kIsWeb) {
          // Web file picker using universal_html
          final input = html.FileUploadInputElement()..accept = '.pdf';
          input.click();
          await input.onChange.first;
          final files = input.files;
          if (files != null && files.isNotEmpty) {
            final file = files[0];
            final reader = html.FileReader();
            reader.readAsArrayBuffer(file);
            await reader.onLoad.first;
            final bytes = reader.result as List<int>;
            request.files.add(http.MultipartFile.fromBytes(
              'permit_pdf',
              bytes,
              filename: file.name,
            ));
          } else {
            _showErrorSnackBar('No PDF file selected');
            return;
          }
        } else {
          // Mobile/desktop file picker (simulated without file_picker package)
          // For demonstration, assume a file is selected from a known path
          // In a real app, you would need a file picker or native integration
          final directory = await getApplicationDocumentsDirectory();
          final filePath = '${directory.path}/permit_document.pdf'; // Placeholder path
          pdfFile = File(filePath);
          if (await pdfFile.exists()) {
            request.files.add(await http.MultipartFile.fromPath(
              'permit_pdf',
              filePath,
            ));
          } else {
            _showErrorSnackBar('Please select a PDF file for approval');
            return;
          }
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        await _fetchAllData();
        _showSuccessSnackBar('Request status updated successfully');
      } else if (response.statusCode == 401) {
        await _handleAuthError();
      } else {
        throw Exception('Failed to update status: ${response.statusCode}');
      }
    } catch (e) {
      _showErrorSnackBar('Error updating request: ${e.toString()}');
    }
  }

  Future<void> _updateAppealStatus(String appealId, String status) async {
    if (appealId.isEmpty) {
      _showErrorSnackBar('Invalid appeal ID');
      return;
    }
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) throw Exception('No token found');

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/admin/appeals/$appealId/status'),
      );
      
      request.headers.addAll({
        'Authorization': 'Bearer $token',
        'Accept': 'application/json',
        'Content-Type': 'multipart/form-data',
      });
      
      request.fields['status'] = status;
      request.fields['decisionNotes'] = 'decision approved';

      // Handle file upload for approval
      if (status == 'approved') {
        File? pdfFile;
        if (kIsWeb) {
          // Web file picker using universal_html
          final input = html.FileUploadInputElement()..accept = '.pdf';
          input.click();
          await input.onChange.first;
          final files = input.files;
          if (files != null && files.isNotEmpty) {
            final file = files[0];
            final reader = html.FileReader();
            reader.readAsArrayBuffer(file);
            await reader.onLoad.first;
            final bytes = reader.result as List<int>;
            request.files.add(http.MultipartFile.fromBytes(
              'permit_pdf',
              bytes,
              filename: file.name,
            ));
          } else {
            _showErrorSnackBar('No PDF file selected for appeal approval');
            return;
          }
        } else {
          // Mobile/desktop file picker (simulated without file_picker package)
          final directory = await getApplicationDocumentsDirectory();
          final filePath = '${directory.path}/permit_document_appeal.pdf';
          pdfFile = File(filePath);
          if (await pdfFile.exists()) {
            request.files.add(await http.MultipartFile.fromPath(
              'permit_pdf',
              filePath,
            ));
          } else {
            _showErrorSnackBar('Please select a PDF file for appeal approval');
            return;
          }
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        await _fetchAllData();
        _showSuccessSnackBar('Appeal status updated successfully');
      } else if (response.statusCode == 401) {
        await _handleAuthError();
      } else {
        throw Exception('Failed to update appeal status: ${response.statusCode}');
      }
    } catch (e) {
      _showErrorSnackBar('Error updating appeal: ${e.toString()}');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: AppColors.pureWhite)),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: AppColors.pureWhite)),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: true);
    final isRtl = localeProvider.currentLanguage == 'ar';
    final isDesktop = MediaQuery.of(context).size.width >= 1200;
    final localizations = AppLocalizations.of(context);

    if (localizations == null) {
      print('Warning: AppLocalizations is null. Using default values.');
    }

    return Directionality(
      textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        backgroundColor: AppColors.lightGray,
        appBar: AppBar(
          backgroundColor: AppColors.pureWhite,
          elevation: 0,
          title: Text(
            localizations?.translate('dashboard') ?? 'Dashboard',
            style: const TextStyle(
              color: AppColors.darkGray,
              fontSize: 22,
              fontWeight: FontWeight.w600,
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh, color: AppColors.primaryOrange),
              onPressed: _fetchAllData,
            ),
            const SizedBox(width: 8),
            DropdownButton<Locale>(
              value: localeProvider.locale,
              icon: const Icon(Icons.language, color: AppColors.primaryOrange),
              underline: const SizedBox(),
              onChanged: (Locale? newLocale) {
                if (newLocale != null && localeProvider.supportedLocales.contains(newLocale)) {
                  localeProvider.setLocale(newLocale);
                }
              },
              items: localeProvider.supportedLocales.map<DropdownMenuItem<Locale>>((Locale value) {
                return DropdownMenuItem<Locale>(
                  value: value,
                  child: Text(
                    value.languageCode == 'en' ? 'English' : 
                    value.languageCode == 'fr' ? 'Français' : 'العربية',
                    style: const TextStyle(color: AppColors.darkGray),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(width: 16),
            IconButton(
              icon: const Icon(Icons.account_circle, color: AppColors.primaryOrange),
              onPressed: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => const AdminProfile()));
              },
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: AppColors.primaryOrange,
            labelColor: AppColors.primaryOrange,
            unselectedLabelColor: AppColors.mediumGray,
            tabs: [
              Tab(
                icon: const Icon(Icons.dashboard),
                text: localizations?.translate('overview') ?? 'Overview',
              ),
              Tab(
                icon: const Icon(Icons.list_alt),
                text: localizations?.translate('requests') ?? 'Requests',
              ),
              Tab(
                icon: const Icon(Icons.feedback),
                text: localizations?.translate('appeals') ?? 'Appeals',
              ),
              Tab(
                icon: const Icon(Icons.card_membership),
                text: localizations?.translate('permits') ?? 'Permits',
              ),
            ],
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator(valueColor: AlwaysStoppedAnimation(AppColors.primaryOrange)))
            : _errorMessage != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: AppColors.error),
                        const SizedBox(height: 16),
                        Text(
                          _errorMessage!,
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: AppColors.darkGray, fontSize: 16),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryOrange,
                            foregroundColor: AppColors.pureWhite,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          ),
                          onPressed: _fetchAllData,
                          child: Text(
                            localizations?.translate('retry') ?? 'Retry',
                            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ],
                    ),
                  )
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildOverviewTab(localizations),
                      _buildRequestsTab(localizations),
                      _buildAppealsTab(localizations),
                      _buildPermitsTab(localizations),
                    ],
                  ),
      ),
    );
  }

  Widget _buildOverviewTab(AppLocalizations? localizations) {
    return RefreshIndicator(
      onRefresh: _fetchAllData,
      color: AppColors.primaryOrange,
      backgroundColor: AppColors.pureWhite,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations?.translate('statistics') ?? 'Statistics',
              style: const TextStyle(
                fontSize: 26,
                fontWeight: FontWeight.w700,
                color: AppColors.darkGray,
              ),
            ),
            const SizedBox(height: 24),
            GridView.count(
              crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.3,
              children: [
                _buildStatCard(
                  title: localizations?.translate('total_requests') ?? 'Total Requests',
                  value: _stats['total_requests']?.toString() ?? '0',
                  icon: Icons.list_alt,
                  color: AppColors.primaryOrange,
                ),
                _buildStatCard(
                  title: localizations?.translate('pending_requests') ?? 'Pending',
                  value: _stats['pending_requests']?.toString() ?? '0',
                  icon: Icons.pending,
                  color: AppColors.warning,
                ),
                _buildStatCard(
                  title: localizations?.translate('approved_requests') ?? 'Approved',
                  value: _stats['approved_requests']?.toString() ?? '0',
                  icon: Icons.check_circle,
                  color: AppColors.success,
                ),
                _buildStatCard(
                  title: localizations?.translate('rejected_requests') ?? 'Rejected',
                  value: _stats['rejected_requests']?.toString() ?? '0',
                  icon: Icons.cancel,
                  color: AppColors.error,
                ),
                _buildStatCard(
                  title: localizations?.translate('total_appeals') ?? 'Total Appeals',
                  value: _stats['total_appeals']?.toString() ?? '0',
                  icon: Icons.feedback,
                  color: AppColors.primaryOrange,
                ),
                _buildStatCard(
                  title: localizations?.translate('pending_appeals') ?? 'Pending Appeals',
                  value: _stats['pending_appeals']?.toString() ?? '0',
                  icon: Icons.feedback_outlined,
                  color: AppColors.warning,
                ),
                _buildStatCard(
                  title: localizations?.translate('total_permits') ?? 'Total Permits',
                  value: _stats['total_permits']?.toString() ?? '0',
                  icon: Icons.card_membership,
                  color: AppColors.success,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: AppColors.pureWhite,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 36, color: color),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(fontSize: 32, fontWeight: FontWeight.w700, color: color),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 14, color: AppColors.mediumGray),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestsTab(AppLocalizations? localizations) {
    return RefreshIndicator(
      onRefresh: _fetchAllData,
      color: AppColors.primaryOrange,
      backgroundColor: AppColors.pureWhite,
      child: _requests.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.inbox, size: 64, color: AppColors.mediumGray),
                  const SizedBox(height: 16),
                  Text(
                    localizations?.translate('no_requests') ?? 'No requests found',
                    style: const TextStyle(fontSize: 16, color: AppColors.darkGray),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _requests.length,
              itemBuilder: (context, index) {
                final request = _requests[index];
                if (request is! Map<String, dynamic>) {
                  print('Invalid request at index $index: $request');
                  return const SizedBox.shrink();
                }
                return _buildRequestCard(request, localizations);
              },
            ),
    );
  }

  Widget _buildRequestCard(Map<String, dynamic> request, AppLocalizations? localizations) {
    print('Building request card: $request');
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: AppColors.pureWhite,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    '${localizations?.translate('request') ?? 'Request'} #${request['id']?.toString() ?? 'N/A'}',
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppColors.darkGray),
                  ),
                ),
                _buildStatusChip(request['status'] as String?),
              ],
            ),
            const SizedBox(height: 12),
            if (request['user'] != null)
              Row(
                children: [
                  Icon(Icons.person, size: 18, color: AppColors.primaryOrange),
                  const SizedBox(width: 8),
                  Text(
                    request['user'] as String? ?? 'Unknown User',
                    style: const TextStyle(fontSize: 14, color: AppColors.mediumGray),
                  ),
                ],
              ),
            if (request['created_at'] != null)
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 18, color: AppColors.primaryOrange),
                  const SizedBox(width: 8),
                  Text(
                    _formatDate(request['created_at'] as String?),
                    style: const TextStyle(fontSize: 14, color: AppColors.mediumGray),
                  ),
                ],
              ),
            const SizedBox(height: 16),
            if (request['buildingDetails'] is String && (request['buildingDetails'] as String).isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.orangePale,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  request['buildingDetails'] as String,
                  style: const TextStyle(fontSize: 14, color: AppColors.darkGray),
                ),
              ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Toujours afficher le bouton pour voir le PDF de la request
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.picture_as_pdf, size: 18, color: AppColors.primaryOrange),
                    label: Text(
                      localizations?.translate('view_document') ?? 'View Document',
                      style: const TextStyle(color: AppColors.primaryOrange),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: AppColors.primaryOrange),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: () async {
                      final token = await _storage.read(key: 'auth_token');
                      if (token != null) {
                        final requestId = request['id']?.toString() ?? '';
                        if (requestId.isNotEmpty) {
                          if (mounted) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PDFViewerPage(
                                  pdfUrl: 'https://api.rokhsati.yakoub-dev.h-s.cloud/api/requests/$requestId/pdf',
                                  token: token,
                                  documentId: requestId,
                                  documentType: 'request', // Nouveau paramètre pour identifier le type
                                ),
                              ),
                            );
                          }
                        } else {
                          _showErrorSnackBar('Request ID not found');
                        }
                      } else {
                        _showErrorSnackBar('Authentication token not found');
                      }
                    },
                  ),
                ),
                const SizedBox(width: 12),
                if (request['status'] == 'pending') ...[
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success,
                      foregroundColor: AppColors.pureWhite,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: () => _showConfirmationDialog(
                      context,
                      localizations?.translate('approve_request_title') ?? 'Approve Request',
                      localizations?.translate('approve_request_message') ?? 'Are you sure you want to approve this request? You will need to upload a permit PDF.',
                      () => _updateRequestStatus(request['id']?.toString() ?? '', 'approved'),
                    ),
                    child: Text(
                      localizations?.translate('approve') ?? 'Approve',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: AppColors.pureWhite,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: () => _showConfirmationDialog(
                      context,
                      localizations?.translate('reject_request_title') ?? 'Reject Request',
                      localizations?.translate('reject_request_message') ?? 'Are you sure you want to reject this request?',
                      () => _updateRequestStatus(request['id']?.toString() ?? '', 'rejected'),
                    ),
                    child: Text(
                      localizations?.translate('reject') ?? 'Reject',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppealsTab(AppLocalizations? localizations) {
    return RefreshIndicator(
      onRefresh: _fetchAllData,
      color: AppColors.primaryOrange,
      backgroundColor: AppColors.pureWhite,
      child: _appeals.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.feedback_outlined, size: 64, color: AppColors.mediumGray),
                  const SizedBox(height: 16),
                  Text(
                    localizations?.translate('no_appeals') ?? 'No appeals found',
                    style: const TextStyle(fontSize: 16, color: AppColors.darkGray),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _appeals.length,
              itemBuilder: (context, index) {
                final appeal = _appeals[index];
                if (appeal is! Map<String, dynamic>) {
                  print('Invalid appeal at index $index: $appeal');
                  return const SizedBox.shrink();
                }
                return _buildAppealCard(appeal, localizations);
              },
            ),
    );
  }

  Widget _buildAppealCard(Map<String, dynamic> appeal, AppLocalizations? localizations) {
    print('Building appeal card: $appeal');
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: AppColors.pureWhite,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    '${localizations?.translate('appeal_for_request') ?? 'Appeal for request'} #${appeal['permitRequestID']?.toString() ?? 'N/A'}',
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppColors.darkGray),
                  ),
                ),
                if (appeal['status'] != null)
                  _buildStatusChip(appeal['status'] as String?, isAppeal: true),
              ],
            ),
            const SizedBox(height: 12),
            if (appeal['timeStamp'] != null)
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 18, color: AppColors.primaryOrange),
                  const SizedBox(width: 8),
                  Text(
                    _formatDate(appeal['timeStamp'] as String?),
                    style: const TextStyle(fontSize: 14, color: AppColors.mediumGray),
                  ),
                ],
              ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.orangePale,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations?.translate('reason') ?? 'Reason:',
                    style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: AppColors.darkGray),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    appeal['content'] as String? ?? 'No reason provided',
                    style: const TextStyle(fontSize: 14, color: AppColors.darkGray),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            if (appeal['status'] == 'pending')
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success,
                      foregroundColor: AppColors.pureWhite,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: () => _showConfirmationDialog(
                      context,
                      localizations?.translate('approve_appeal_title') ?? 'Approve Appeal',
                      localizations?.translate('approve_appeal_message') ?? 'Are you sure you want to approve this appeal? You will need to upload a permit PDF.',
                      () => _updateAppealStatus(appeal['permitRequestID']?.toString() ?? '', 'approved'),
                    ),
                    child: Text(
                      localizations?.translate('approve') ?? 'Approve',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: AppColors.pureWhite,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: () => _showConfirmationDialog(
                      context,
                      localizations?.translate('reject_appeal_title') ?? 'Reject Appeal',
                      localizations?.translate('reject_appeal_message') ?? 'Are you sure you want to reject this appeal?',
                      () => _updateAppealStatus(appeal['permitRequestID']?.toString() ?? '', 'rejected'),
                    ),
                    child: Text(
                      localizations?.translate('reject') ?? 'Reject',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermitsTab(AppLocalizations? localizations) {
    return RefreshIndicator(
      onRefresh: _fetchAllData,
      color: AppColors.primaryOrange,
      backgroundColor: AppColors.pureWhite,
      child: _permits.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.card_membership_outlined, size: 64, color: AppColors.mediumGray),
                  const SizedBox(height: 16),
                  Text(
                    localizations?.translate('no_permits') ?? 'No permits found',
                    style: const TextStyle(fontSize: 16, color: AppColors.darkGray),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _permits.length,
              itemBuilder: (context, index) {
                final permit = _permits[index];
                if (permit is! Map<String, dynamic>) {
                  print('Invalid permit at index $index: $permit');
                  return const SizedBox.shrink();
                }
                return _buildPermitCard(permit, localizations);
              },
            ),
    );
  }

  Widget _buildPermitCard(Map<String, dynamic> permit, AppLocalizations? localizations) {
    print('Building permit card: $permit');
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: AppColors.pureWhite,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${localizations?.translate('permit') ?? 'Permit'} #${permit['permitID']?.toString() ?? 'N/A'}',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppColors.darkGray),
                ),
                if (permit['status'] != null)
                  _buildStatusChip(permit['status'] as String?),
              ],
            ),
            const SizedBox(height: 12),
            if (permit['user'] != null)
              Row(
                children: [
                  Icon(Icons.person, size: 18, color: AppColors.primaryOrange),
                  const SizedBox(width: 8),
                  Text(
                    permit['user'] as String? ?? 'Unknown User',
                    style: const TextStyle(fontSize: 14, color: AppColors.mediumGray),
                  ),
                ],
              ),
            if (permit['issueDate'] != null)
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 18, color: AppColors.primaryOrange),
                  const SizedBox(width: 8),
                  Text(
                    '${localizations?.translate('issued_at') ?? 'Issued at'}: ${_formatDate(permit['issueDate'] as String?)}',
                    style: const TextStyle(fontSize: 14, color: AppColors.mediumGray),
                  ),
                ],
              ),
            if (permit['expiryDate'] != null)
              Row(
                children: [
                  Icon(Icons.event_busy, size: 18, color: AppColors.primaryOrange),
                  const SizedBox(width: 8),
                  Text(
                    '${localizations?.translate('expires_at') ?? 'Expires at'}: ${_formatDate(permit['expiryDate'] as String?)}',
                    style: const TextStyle(fontSize: 14, color: AppColors.mediumGray),
                  ),
                ],
              ),
            const SizedBox(height: 16),
            if (permit['permit_pdf_url'] is String && (permit['permit_pdf_url'] as String).isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.orangePale,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      localizations?.translate('permit_document') ?? 'Permit Document:',
                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: AppColors.darkGray),
                    ),
                    const SizedBox(height: 8),
                    OutlinedButton.icon(
                      icon: const Icon(Icons.picture_as_pdf, size: 18, color: AppColors.primaryOrange),
                      label: Text(
                        localizations?.translate('view_document') ?? 'View Document',
                        style: const TextStyle(color: AppColors.primaryOrange),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppColors.primaryOrange),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      onPressed: () async {
                        final token = await _storage.read(key: 'auth_token');
                        if (token != null) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => PDFViewerPage(
                                pdfUrl: permit['permit_pdf_url'] as String,
                                token: token,
                                documentId: permit['permitID']?.toString() ?? '',
                                documentType: 'permit',
                              ),
                            ),
                          );
                        } else {
                          _showErrorSnackBar('Authentication token not found');
                        }
                      },
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String? status, {bool isAppeal = false}) {
    final localizations = AppLocalizations.of(context);
    Color backgroundColor;
    Color textColor = AppColors.pureWhite;
    String displayStatus;

    switch (status?.toLowerCase()) {
      case 'approved':
        backgroundColor = AppColors.success;
        displayStatus = isAppeal
            ? (localizations?.getAppealStatusLabel('approved') ?? 'APPEAL APPROVED')
            : (localizations?.getRequestStatusLabel('approved') ?? 'REQUEST APPROVED');
        break;
      case 'rejected':
        backgroundColor = AppColors.error;
        displayStatus = isAppeal
            ? (localizations?.getAppealStatusLabel('rejected') ?? 'APPEAL REJECTED')
            : (localizations?.getRequestStatusLabel('rejected') ?? 'REQUEST REJECTED');
        break;
      case 'pending':
        backgroundColor = AppColors.warning;
        displayStatus = isAppeal
            ? (localizations?.getAppealStatusLabel('pending') ?? 'APPEAL PENDING')
            : (localizations?.getRequestStatusLabel('pending') ?? 'REQUEST PENDING');
        break;
      default:
        backgroundColor = AppColors.mediumGray;
        displayStatus = localizations?.translate('unknown_status') ?? 'UNKNOWN';
    }

    return Chip(
      label: Text(
        displayStatus,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: backgroundColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  void _showConfirmationDialog(
    BuildContext context,
    String title,
    String message,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.pureWhite,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          title: Text(
            title,
            style: const TextStyle(color: AppColors.darkGray, fontWeight: FontWeight.w600),
          ),
          content: Text(
            message,
            style: const TextStyle(color: AppColors.mediumGray),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                AppLocalizations.of(context)?.translate('cancel') ?? 'Cancel',
                style: const TextStyle(color: AppColors.mediumGray),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryOrange,
                foregroundColor: AppColors.pureWhite,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                onConfirm();
              },
              child: Text(
                AppLocalizations.of(context)?.translate('confirm') ?? 'Confirm',
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        );
      },
    );
  }
}

class PDFViewerPage extends StatefulWidget {
  final String pdfUrl;
  final String token;
  final String documentId;
  final String documentType; // 'request', 'permit', ou 'appeal'

  const PDFViewerPage({
    super.key,
    required this.pdfUrl,
    required this.token,
    required this.documentId,
    this.documentType = 'permit', // Valeur par défaut pour la compatibilité
  });

  @override
  _PDFViewerPageState createState() => _PDFViewerPageState();
}

class _PDFViewerPageState extends State<PDFViewerPage> {
  String? _localPath;
  String? _pdfDataUrl;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  Future<void> _loadPdf() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      print('PDF URL: ${widget.pdfUrl}, Document ID: ${widget.documentId}, Type: ${widget.documentType}');

      // Construire l'endpoint approprié selon le type de document
      final Uri pdfEndpoint;
      if (widget.pdfUrl.startsWith('http')) {
        // URL complète fournie
        pdfEndpoint = Uri.parse(widget.pdfUrl);
      } else {
        // Construire l'URL selon le type de document
        switch (widget.documentType) {
          case 'request':
            pdfEndpoint = Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/requests/${widget.documentId}/pdf');
            break;
          case 'appeal':
            pdfEndpoint = Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/appeals/${widget.documentId}/pdf');
            break;
          case 'permit':
          default:
            pdfEndpoint = Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/permits/${widget.documentId}/pdf');
            break;
        }
      }
      print('Fetching PDF from: $pdfEndpoint');

      final response = await http.get(
        pdfEndpoint,
        headers: {
          'Authorization': 'Bearer ${widget.token}',
          'Accept': 'application/pdf',
        },
      );

      print('Response status: ${response.statusCode}');
      print('Response body length: ${response.bodyBytes.length}');
      print('Response headers: ${response.headers}');

      if (response.statusCode == 200) {
        if (kIsWeb) {
          final base64String = base64Encode(response.bodyBytes);
          _pdfDataUrl = 'data:application/pdf;base64,$base64String';
          html.document.body?.children.clear();
          final iframe = html.IFrameElement()
            ..src = _pdfDataUrl!
            ..style.border = 'none'
            ..style.width = '100%'
            ..style.height = '100%';
          html.document.body?.append(iframe);
          setState(() => _isLoading = false);
        } else {
          try {
            final directory = await getApplicationDocumentsDirectory();
            if (directory == null) {
              throw Exception('Could not access application documents directory');
            }
            final filePath = '${directory.path}/temp.pdf';
            print('Writing PDF to: $filePath');
            final file = File(filePath);
            await file.writeAsBytes(response.bodyBytes);
            print('File written, length: ${await file.length()}');

            if (await file.exists()) {
              setState(() {
                _localPath = filePath;
                _isLoading = false;
              });
            } else {
              throw Exception('File does not exist after writing');
            }
          } catch (e) {
            setState(() {
              _errorMessage = 'Error accessing directory or writing file: $e';
              _isLoading = false;
            });
            print('Directory or file error: $e');
          }
        }
      } else {
        throw Exception('Failed to load PDF. Status: ${response.statusCode}, Body: ${response.body.substring(0, 100)}...');
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading PDF: $e';
        _isLoading = false;
      });
      print('Error in _loadPdf: $e');
    }
  }

  @override
  void dispose() {
    if (_localPath != null && !kIsWeb) {
      final file = File(_localPath!);
      if (file.existsSync()) {
        file.deleteSync();
        print('Deleted temporary file: $_localPath');
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGray,
      appBar: AppBar(
        backgroundColor: AppColors.pureWhite,
        elevation: 0,
        title: Text(
          AppLocalizations.of(context)?.translate('view_document') ?? 'View Document',
          style: const TextStyle(color: AppColors.darkGray, fontSize: 22, fontWeight: FontWeight.w600),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.primaryOrange),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(valueColor: AlwaysStoppedAnimation(AppColors.primaryOrange)))
          : kIsWeb
              ? const SizedBox()
              : _localPath != null
                  ? PDFView(
                      filePath: _localPath,
                      enableSwipe: true,
                      swipeHorizontal: true,
                      autoSpacing: false,
                      pageFling: false,
                      onError: (error) {
                        print('PDFView Error: $error');
                        setState(() {
                          _errorMessage = 'PDF rendering error: $error';
                        });
                      },
                      onPageChanged: (page, total) {
                        print('Page changed to: $page/$total');
                      },
                    )
                  : Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error_outline, size: 64, color: AppColors.error),
                          const SizedBox(height: 16),
                          Text(
                            _errorMessage ?? 'Failed to load PDF',
                            textAlign: TextAlign.center,
                            style: const TextStyle(color: AppColors.darkGray, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
    );
  }
}