import 'package:flutter/material.dart';
import '../src/config/app_localizations.dart';
import '../src/config/theme_helper.dart';

/// AppBar uniforme pour toutes les pages de visualisation PDF
/// Contient seulement une icône de retour à l'accueil
class PDFAppBar extends StatelessWidget implements PreferredSizeWidget {
  const PDFAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return AppBar(
      backgroundColor: AppColors.pureWhite,
      elevation: 0,
      automaticallyImplyLeading: false, // Désactive le bouton retour par défaut
      centerTitle: true,
      actions: [
        // Icône de retour à l'accueil
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: IconButton(
            icon: const Icon(
              Icons.home,
              color: AppColors.primaryOrange,
              size: 28,
            ),
            tooltip: localizations?.translate('back_to_home') ?? 'Back to Home',
            onPressed: () {
              // Retourner à la page d'accueil (Dashboard)
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// AppBar alternative avec icône de fermeture pour les modals
class PDFModalAppBar extends StatelessWidget implements PreferredSizeWidget {
  const PDFModalAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return AppBar(
      backgroundColor: AppColors.pureWhite,
      elevation: 0,
      automaticallyImplyLeading: false,
      centerTitle: true,
      actions: [
        // Icône de fermeture pour les modals
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: IconButton(
            icon: const Icon(
              Icons.close,
              color: AppColors.primaryOrange,
              size: 28,
            ),
            tooltip: localizations?.translate('cancel') ?? 'Close',
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// AppBar avec titre personnalisé (optionnel)
class PDFAppBarWithTitle extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final bool showHomeButton;
  
  const PDFAppBarWithTitle({
    super.key,
    this.title,
    this.showHomeButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return AppBar(
      backgroundColor: AppColors.pureWhite,
      elevation: 0,
      automaticallyImplyLeading: false,
      centerTitle: true,
      title: title != null 
          ? Text(
              title!,
              style: const TextStyle(
                color: AppColors.darkGray,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            )
          : null,
      actions: showHomeButton ? [
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: IconButton(
            icon: const Icon(
              Icons.home,
              color: AppColors.primaryOrange,
              size: 28,
            ),
            tooltip: localizations?.translate('back_to_home') ?? 'Back to Home',
            onPressed: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
          ),
        ),
      ] : [
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: IconButton(
            icon: const Icon(
              Icons.close,
              color: AppColors.primaryOrange,
              size: 28,
            ),
            tooltip: localizations?.translate('cancel') ?? 'Close',
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
