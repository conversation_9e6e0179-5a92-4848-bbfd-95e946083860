import 'package:flutter/material.dart';

class AppLocalizations {
  AppLocalizations(this.locale);

  final Locale locale;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  // Mots techniques qui restent sans traduction
  static const Set<String> _technicalTerms = {
    'PDF', 'ID', 'approved', 'rejected', 'pending', 'admin', 'email'
  };

  static const Map<String, Map<String, String>> _translations = {
    'fr': {
      // Authentification
      'login': 'Connexion',
      'username': 'Nom d\'utilisateur',
      'enter_username': 'Entrez votre nom d\'utilisateur',
      'password': 'Mot de passe',
      'enter_password': 'Entrez votre mot de passe',
      'password_min_length': 'Le mot de passe doit contenir au moins 8 caractères',
      'login_failed': 'Échec de la connexion',
      'network_error': 'Erreur réseau',
      'forgot_password': 'Mot de passe oublié ?',
      'sign_in': 'Se connecter',

      // Navigation
      'dashboard': 'Tableau de bord',
      'requests': 'Demandes',
      'recourse': 'Recours',
      'profile': 'Profil',
      'overview': 'Aperçu',
      'appeals': 'Recours',
      'permits': 'Permis',

      // Profil utilisateur
      'name': 'Nom',
      'email': 'Courriel',
      'role': 'Rôle',
      'administrator': 'Administrateur',
      'logout': 'Déconnexion',
      'edit_profile': 'Modifier le profil',
      'confirm_logout': 'Confirmer la déconnexion',
      'are_you_sure_logout': 'Êtes-vous sûr de vouloir vous déconnecter ?',

      // Actions générales
      'retry': 'Réessayer',
      'cancel': 'Annuler',
      'confirm': 'Confirmer',
      'approve': 'Approuver',
      'reject': 'Rejeter',
      'view_document': 'Voir le document',
      'save': 'Enregistrer',
      'delete': 'Supprimer',
      'edit': 'Modifier',
      'add': 'Ajouter',
      'search': 'Rechercher',
      'filter': 'Filtrer',
      'sort': 'Trier',
      'refresh': 'Actualiser',

      // Messages d'état
      'no_requests': 'Aucune demande trouvée',
      'no_appeals': 'Aucun recours trouvé',
      'no_permits': 'Aucun permis trouvé',
      'loading': 'Chargement...',
      'no_data': 'Aucune donnée disponible',

      // Entités
      'request': 'Demande',
      'permit': 'Permis',
      'appeal_for_request': 'Recours pour demande',
      'reason': 'Raison',
      'issued_at': 'Émis le',
      'expires_at': 'Expire le',
      'permit_document': 'Document de permis',
      'created_at': 'Créé le',
      'updated_at': 'Mis à jour le',

      // Statistiques
      'statistics': 'Statistiques',
      'total_requests': 'Total des demandes',
      'pending_requests': 'En attente',
      'approved_requests': 'Approuvées',
      'rejected_requests': 'Rejetées',
      'total_appeals': 'Total des recours',
      'pending_appeals': 'Recours en attente',
      'total_permits': 'Total des permis',

      // Messages d'erreur et de succès
      'invalid_request_id': 'Identifiant de demande invalide',
      'invalid_appeal_id': 'Identifiant de recours invalide',
      'no_pdf_selected': 'Aucun fichier PDF sélectionné',
      'no_pdf_selected_appeal': 'Aucun fichier PDF sélectionné pour l\'approbation du recours',
      'select_pdf_approval': 'Veuillez sélectionner un fichier PDF pour l\'approbation',
      'select_pdf_appeal_approval': 'Veuillez sélectionner un fichier PDF pour l\'approbation du recours',
      'request_updated_success': 'Statut de la demande mis à jour avec succès',
      'appeal_updated_success': 'Statut du recours mis à jour avec succès',
      'error_updating_request': 'Erreur lors de la mise à jour de la demande',
      'error_updating_appeal': 'Erreur lors de la mise à jour du recours',
      'request_id_not_found': 'Identifiant de demande introuvable',
      'auth_token_not_found': 'Jeton d\'authentification introuvable',
      'unknown_user': 'Utilisateur inconnu',
      'no_reason_provided': 'Aucune raison fournie',
      'unknown_status': 'Statut inconnu',
      'date_not_available': 'Non disponible',
      'failed_load_pdf': 'Échec du chargement du PDF',
      'operation_success': 'Opération réussie',
      'operation_failed': 'Opération échouée',

      // Dialogues de confirmation
      'approve_request_title': 'Approuver la demande',
      'approve_request_message': 'Êtes-vous sûr de vouloir approuver cette demande ? Vous devrez télécharger un PDF de permis.',
      'reject_request_title': 'Rejeter la demande',
      'reject_request_message': 'Êtes-vous sûr de vouloir rejeter cette demande ?',
      'approve_appeal_title': 'Approuver le recours',
      'approve_appeal_message': 'Êtes-vous sûr de vouloir approuver ce recours ? Vous devrez télécharger un PDF de permis.',
      'reject_appeal_title': 'Rejeter le recours',
      'reject_appeal_message': 'Êtes-vous sûr de vouloir rejeter ce recours ?',

      // Statuts (gardent les termes techniques)
      'status_approved': 'approved',
      'status_rejected': 'rejected',
      'status_pending': 'pending',

      // Langues
      'language_english': 'English',
      'language_french': 'Français',
      'language_arabic': 'العربية',
    },
    'ar': {
      // المصادقة
      'login': 'تسجيل الدخول',
      'username': 'اسم المستخدم',
      'enter_username': 'أدخل اسم المستخدم',
      'password': 'كلمة المرور',
      'enter_password': 'أدخل كلمة المرور',
      'password_min_length': 'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل',
      'login_failed': 'فشل تسجيل الدخول',
      'network_error': 'خطأ في الشبكة',
      'forgot_password': 'نسيت كلمة المرور؟',
      'sign_in': 'تسجيل الدخول',

      // التنقل
      'dashboard': 'لوحة التحكم',
      'requests': 'الطلبات',
      'recourse': 'الطعون',
      'profile': 'الملف الشخصي',
      'overview': 'نظرة عامة',
      'appeals': 'الطعون',
      'permits': 'التصاريح',

      // ملف المستخدم
      'name': 'الاسم',
      'email': 'البريد الإلكتروني',
      'role': 'الدور',
      'administrator': 'مدير',
      'logout': 'تسجيل الخروج',
      'edit_profile': 'تعديل الملف الشخصي',
      'confirm_logout': 'تأكيد تسجيل الخروج',
      'are_you_sure_logout': 'هل أنت متأكد من أنك تريد تسجيل الخروج؟',

      // الإجراءات العامة
      'retry': 'أعد المحاولة',
      'cancel': 'إلغاء',
      'confirm': 'تأكيد',
      'approve': 'موافقة',
      'reject': 'رفض',
      'view_document': 'عرض الوثيقة',
      'save': 'حفظ',
      'delete': 'حذف',
      'edit': 'تعديل',
      'add': 'إضافة',
      'search': 'بحث',
      'filter': 'تصفية',
      'sort': 'ترتيب',
      'refresh': 'تحديث',

      // رسائل الحالة
      'no_requests': 'لا توجد طلبات',
      'no_appeals': 'لا توجد طعون',
      'no_permits': 'لا توجد تصاريح',
      'loading': 'جاري التحميل...',
      'no_data': 'لا توجد بيانات متاحة',

      // الكيانات
      'request': 'طلب',
      'permit': 'تصريح',
      'appeal_for_request': 'طعن للطلب',
      'reason': 'السبب',
      'issued_at': 'تم الإصدار في',
      'expires_at': 'ينتهي في',
      'permit_document': 'وثيقة التصريح',
      'created_at': 'تم الإنشاء في',
      'updated_at': 'تم التحديث في',

      // الإحصائيات
      'statistics': 'إحصائيات',
      'total_requests': 'إجمالي الطلبات',
      'pending_requests': 'قيد الانتظار',
      'approved_requests': 'تمت الموافقة عليها',
      'rejected_requests': 'مرفوضة',
      'total_appeals': 'إجمالي الطعون',
      'pending_appeals': 'طعون قيد الانتظار',
      'total_permits': 'إجمالي التصاريح',

      // رسائل الخطأ والنجاح
      'invalid_request_id': 'معرف الطلب غير صالح',
      'invalid_appeal_id': 'معرف الطعن غير صالح',
      'no_pdf_selected': 'لم يتم تحديد ملف PDF',
      'no_pdf_selected_appeal': 'لم يتم تحديد ملف PDF لموافقة الطعن',
      'select_pdf_approval': 'يرجى تحديد ملف PDF للموافقة',
      'select_pdf_appeal_approval': 'يرجى تحديد ملف PDF لموافقة الطعن',
      'request_updated_success': 'تم تحديث حالة الطلب بنجاح',
      'appeal_updated_success': 'تم تحديث حالة الطعن بنجاح',
      'error_updating_request': 'خطأ في تحديث الطلب',
      'error_updating_appeal': 'خطأ في تحديث الطعن',
      'request_id_not_found': 'معرف الطلب غير موجود',
      'auth_token_not_found': 'رمز المصادقة غير موجود',
      'unknown_user': 'مستخدم غير معروف',
      'no_reason_provided': 'لم يتم تقديم سبب',
      'unknown_status': 'حالة غير معروفة',
      'date_not_available': 'غير متاح',
      'failed_load_pdf': 'فشل في تحميل PDF',
      'operation_success': 'تمت العملية بنجاح',
      'operation_failed': 'فشلت العملية',

      // حوارات التأكيد
      'approve_request_title': 'موافقة على الطلب',
      'approve_request_message': 'هل أنت متأكد من أنك تريد الموافقة على هذا الطلب؟ ستحتاج إلى رفع ملف PDF للتصريح.',
      'reject_request_title': 'رفض الطلب',
      'reject_request_message': 'هل أنت متأكد من أنك تريد رفض هذا الطلب؟',
      'approve_appeal_title': 'موافقة على الطعن',
      'approve_appeal_message': 'هل أنت متأكد من أنك تريد الموافقة على هذا الطعن؟ ستحتاج إلى رفع ملف PDF للتصريح.',
      'reject_appeal_title': 'رفض الطعن',
      'reject_appeal_message': 'هل أنت متأكد من أنك تريد رفض هذا الطعن؟',

      // الحالات (تحتفظ بالمصطلحات التقنية)
      'status_approved': 'approved',
      'status_rejected': 'rejected',
      'status_pending': 'pending',

      // اللغات
      'language_english': 'English',
      'language_french': 'Français',
      'language_arabic': 'العربية',
    },
    'en': {
      // Authentication
      'login': 'Login',
      'username': 'Username',
      'enter_username': 'Enter your username',
      'password': 'Password',
      'enter_password': 'Enter your password',
      'password_min_length': 'Password must be at least 8 characters long',
      'login_failed': 'Login failed',
      'network_error': 'Network error',
      'forgot_password': 'Forgot Password?',
      'sign_in': 'Sign In',

      // Navigation
      'dashboard': 'Dashboard',
      'requests': 'Requests',
      'recourse': 'Recourse',
      'profile': 'Profile',
      'overview': 'Overview',
      'appeals': 'Appeals',
      'permits': 'Permits',

      // User Profile
      'name': 'Name',
      'email': 'Email',
      'role': 'Role',
      'administrator': 'Administrator',
      'logout': 'Logout',
      'edit_profile': 'Edit Profile',
      'confirm_logout': 'Confirm Logout',
      'are_you_sure_logout': 'Are you sure you want to log out?',

      // General Actions
      'retry': 'Retry',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'approve': 'Approve',
      'reject': 'Reject',
      'view_document': 'View Document',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'search': 'Search',
      'filter': 'Filter',
      'sort': 'Sort',
      'refresh': 'Refresh',

      // Status Messages
      'no_requests': 'No requests found',
      'no_appeals': 'No appeals found',
      'no_permits': 'No permits found',
      'loading': 'Loading...',
      'no_data': 'No data available',

      // Entities
      'request': 'Request',
      'permit': 'Permit',
      'appeal_for_request': 'Appeal for request',
      'reason': 'Reason',
      'issued_at': 'Issued at',
      'expires_at': 'Expires at',
      'permit_document': 'Permit Document',
      'created_at': 'Created at',
      'updated_at': 'Updated at',

      // Statistics
      'statistics': 'Statistics',
      'total_requests': 'Total Requests',
      'pending_requests': 'Pending',
      'approved_requests': 'Approved',
      'rejected_requests': 'Rejected',
      'total_appeals': 'Total Appeals',
      'pending_appeals': 'Pending Appeals',
      'total_permits': 'Total Permits',

      // Error and Success Messages
      'invalid_request_id': 'Invalid request ID',
      'invalid_appeal_id': 'Invalid appeal ID',
      'no_pdf_selected': 'No PDF file selected',
      'no_pdf_selected_appeal': 'No PDF file selected for appeal approval',
      'select_pdf_approval': 'Please select a PDF file for approval',
      'select_pdf_appeal_approval': 'Please select a PDF file for appeal approval',
      'request_updated_success': 'Request status updated successfully',
      'appeal_updated_success': 'Appeal status updated successfully',
      'error_updating_request': 'Error updating request',
      'error_updating_appeal': 'Error updating appeal',
      'request_id_not_found': 'Request ID not found',
      'auth_token_not_found': 'Authentication token not found',
      'unknown_user': 'Unknown User',
      'no_reason_provided': 'No reason provided',
      'unknown_status': 'Unknown status',
      'date_not_available': 'Not available',
      'failed_load_pdf': 'Failed to load PDF',
      'operation_success': 'Operation successful',
      'operation_failed': 'Operation failed',

      // Confirmation Dialogs
      'approve_request_title': 'Approve Request',
      'approve_request_message': 'Are you sure you want to approve this request? You will need to upload a permit PDF.',
      'reject_request_title': 'Reject Request',
      'reject_request_message': 'Are you sure you want to reject this request?',
      'approve_appeal_title': 'Approve Appeal',
      'approve_appeal_message': 'Are you sure you want to approve this appeal? You will need to upload a permit PDF.',
      'reject_appeal_title': 'Reject Appeal',
      'reject_appeal_message': 'Are you sure you want to reject this appeal?',

      // Status Labels (keep technical terms)
      'status_approved': 'approved',
      'status_rejected': 'rejected',
      'status_pending': 'pending',

      // Languages
      'language_english': 'English',
      'language_french': 'Français',
      'language_arabic': 'العربية',
    },
  };

  /// Vérifie si un terme doit rester sans traduction
  bool _isTechnicalTerm(String term) {
    return _technicalTerms.contains(term.toLowerCase());
  }

  /// Traduit une clé en tenant compte des termes techniques
  String translate(String key) {
    // Si c'est un terme technique, on le retourne tel quel
    if (_isTechnicalTerm(key)) {
      return key;
    }

    final translations = _translations[locale.languageCode];
    if (translations != null && translations.containsKey(key)) {
      final translation = translations[key]!;
      // Si la traduction est un terme technique, on le retourne tel quel
      if (_isTechnicalTerm(translation)) {
        return translation;
      }
      return translation;
    }

    final fallback = _translations['en'];
    if (fallback != null && fallback.containsKey(key)) {
      final translation = fallback[key]!;
      // Si la traduction est un terme technique, on le retourne tel quel
      if (_isTechnicalTerm(translation)) {
        return translation;
      }
      return translation;
    }

    return key; // Fallback to key itself if no translation found
  }

  /// Méthode utilitaire pour obtenir le statut formaté
  String getStatusLabel(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'approved';
      case 'rejected':
        return 'rejected';
      case 'pending':
        return 'pending';
      default:
        return translate('unknown_status');
    }
  }

  /// Méthode utilitaire pour formater les dates
  String formatDate(DateTime? date) {
    if (date == null) {
      return translate('date_not_available');
    }
    // Format basique - peut être amélioré avec intl package
    return '${date.day}/${date.month}/${date.year}';
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => ['en', 'fr', 'ar'].contains(locale.languageCode);

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}