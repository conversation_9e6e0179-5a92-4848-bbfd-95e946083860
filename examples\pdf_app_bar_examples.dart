import 'package:flutter/material.dart';
import '../lib/widgets/pdf_app_bar.dart';
import '../lib/src/config/theme_helper.dart';

/// Exemples d'utilisation des nouveaux AppBar pour PDF
class PDFAppBarExamples {
  
  /// Exemple 1: Visualiseur PDF standard avec AppBar uniforme
  Widget buildStandardPDFViewer(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGray,
      appBar: const PDFAppBar(), // AppBar uniforme avec icône home
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.picture_as_pdf,
              size: 64,
              color: AppColors.primaryOrange,
            ),
            SizedBox(height: 16),
            Text(
              'PDF Content Here',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.darkGray,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Appuyez sur l\'icône home pour retourner à l\'accueil',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.mediumGray,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  /// Exemple 2: PDF en modal avec AppBar de fermeture
  Widget buildModalPDFViewer(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGray,
      appBar: const PDFModalAppBar(), // AppBar avec icône close
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.picture_as_pdf,
              size: 64,
              color: AppColors.primaryOrange,
            ),
            SizedBox(height: 16),
            Text(
              'PDF Modal Content',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.darkGray,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Appuyez sur l\'icône close pour fermer',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.mediumGray,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  /// Exemple 3: PDF avec titre personnalisé
  Widget buildTitledPDFViewer(BuildContext context, String documentTitle) {
    return Scaffold(
      backgroundColor: AppColors.lightGray,
      appBar: PDFAppBarWithTitle(
        title: documentTitle,
        showHomeButton: true, // Affiche l'icône home
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.picture_as_pdf,
              size: 64,
              color: AppColors.primaryOrange,
            ),
            const SizedBox(height: 16),
            Text(
              documentTitle,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.darkGray,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'PDF avec titre personnalisé',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.mediumGray,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Exemple 4: Ouverture de PDF depuis une liste
  Widget buildPDFListItem(
    BuildContext context,
    String documentName,
    String documentUrl,
    String documentType,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: const Icon(
          Icons.picture_as_pdf,
          color: AppColors.primaryOrange,
          size: 32,
        ),
        title: Text(
          documentName,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.darkGray,
          ),
        ),
        subtitle: Text(
          'Type: $documentType',
          style: const TextStyle(
            color: AppColors.mediumGray,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppColors.primaryOrange,
          size: 16,
        ),
        onTap: () {
          // Ouvrir le PDF avec l'AppBar uniforme
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => _buildPDFViewerPage(
                documentUrl,
                documentName,
                documentType,
              ),
            ),
          );
        },
      ),
    );
  }
  
  /// Page PDF avec AppBar uniforme
  Widget _buildPDFViewerPage(
    String pdfUrl,
    String documentName,
    String documentType,
  ) {
    return Scaffold(
      backgroundColor: AppColors.lightGray,
      appBar: const PDFAppBar(), // AppBar uniforme
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.picture_as_pdf,
              size: 80,
              color: AppColors.primaryOrange,
            ),
            const SizedBox(height: 24),
            Text(
              documentName,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.darkGray,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Type: $documentType',
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.mediumGray,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'URL: $pdfUrl',
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.mediumGray,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            const Text(
              '🏠 Appuyez sur l\'icône home en haut à droite\npour retourner à l\'accueil',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.primaryOrange,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  /// Exemple 5: Démonstration des différents AppBar
  Widget buildAppBarDemo(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGray,
      appBar: AppBar(
        title: const Text('PDF AppBar Demo'),
        backgroundColor: AppColors.primaryOrange,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          const Text(
            'Exemples d\'AppBar pour PDF',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.darkGray,
            ),
          ),
          const SizedBox(height: 24),
          
          // AppBar Standard
          _buildDemoCard(
            context,
            'AppBar Standard',
            'Icône home - Retour à l\'accueil',
            Icons.home,
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => buildStandardPDFViewer(context),
              ),
            ),
          ),
          
          // AppBar Modal
          _buildDemoCard(
            context,
            'AppBar Modal',
            'Icône close - Fermer le modal',
            Icons.close,
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => buildModalPDFViewer(context),
              ),
            ),
          ),
          
          // AppBar avec titre
          _buildDemoCard(
            context,
            'AppBar avec Titre',
            'Titre personnalisé + icône home',
            Icons.title,
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => buildTitledPDFViewer(
                  context,
                  'Document de Permis #123',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildDemoCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        leading: Icon(
          icon,
          color: AppColors.primaryOrange,
          size: 32,
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.darkGray,
          ),
        ),
        subtitle: Text(
          description,
          style: const TextStyle(
            color: AppColors.mediumGray,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppColors.primaryOrange,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }
}
